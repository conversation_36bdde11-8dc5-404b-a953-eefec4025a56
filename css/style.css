html,
body {
  height: 105%;
overflow: hidden;
}
.bg {
  background-image: url(../img/bg.jpg);
  overflow: hidden;
  background-color: #121936;
  background-size: 100% 100% ;
  background-position: center center;
  background-repeat: no-repeat;
}

a {
  color: #fff;
}

.buttons {
  position: absolute;
  bottom: 10px;
  width: 100%;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding-left:10px;
}

.next {
  font-size: 40px;
  color: #fff;
  line-height: 1.5;
  padding: 20px;
  text-align: center;
  position: absolute;
  width: 100%;
  text-shadow: 0px 1px 0px #999, 0px 2px 0px #888, 0px 3px 0px #777, 0px 4px 0px #666, 0px 5px 0px #555, 0px 6px 0px #444, 0px 7px 0px #333, 0px 8px 7px #001135;
}
 .titleText {
       text-align: center;
      font-size: 65px;
      padding-top: 40px;
      color: gold;
      letter-spacing: 0;
      font-family: '楷体';
      text-shadow: 0px 1px 0px #999, 0px 2px 0px #888, 0px 3px 0px #777, 0px 4px 0px #666, 0px 5px 0px #555, 0px 6px 0px #444, 0px 7px 0px #333, 0px 8px 7px #001135;
    }
.next .title {
  color: yellow;
  text-shadow: 0px 1px 0px #999, 0px 2px 0px #888, 0px 3px 0px #777, 0px 4px 0px #666, 0px 5px 0px #555, 0px 6px 0px #444, 0px 7px 0px #333, 0px 8px 7px #001135;
}
.el-select {
  width: 120px;
  margin-right: 10px;
}
.batch {
  position: absolute;
  width: 50%;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  top: 50%;
  color: #fff;
  left: 50%;
  transform: translate(-50%, -50%);
}
.player {
  display: inline-block;
  text-align: center;
  font-size: 15px;
  width: 120px;
  background: #fff;
  line-height: 15px;
  color: #000;
  margin: 5px;
  border-radius: 10px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.8);
  padding: 10px 0;
}

.lucky-dog {
  display: none;
  text-align: center;
  width: 200px;
  background: #fff;
  font-size: 10px;
  line-height: 20px;
  color: #000;
  margin: 1px;
  border-radius: 10px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.8);
  padding: 1px 0;
}

.result {
  width: 200px;
  position: absolute;
  right: 10px;
  top: 10px;
  color: #fff;
  line-height: 1.5;
  font-size: 18px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

