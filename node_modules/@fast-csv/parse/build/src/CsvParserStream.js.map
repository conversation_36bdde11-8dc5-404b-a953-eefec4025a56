{"version": 3, "file": "CsvParserStream.js", "sourceRoot": "", "sources": ["../../src/CsvParserStream.ts"], "names": [], "mappings": ";;;AAAA,mDAA+C;AAC/C,mCAAsD;AAEtD,6CAA0E;AAC1E,qCAAkC;AAGlC,MAAa,eAA8C,SAAQ,kBAAS;IAuBxE,YAAmB,aAA4B;QAC3C,KAAK,CAAC,EAAE,UAAU,EAAE,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;QAb5C,UAAK,GAAG,EAAE,CAAC;QAEX,aAAQ,GAAG,CAAC,CAAC;QAEb,mBAAc,GAAG,CAAC,CAAC;QAEnB,oBAAe,GAAG,CAAC,CAAC;QAEpB,eAAU,GAAG,KAAK,CAAC;QAEnB,mBAAc,GAAG,KAAK,CAAC;QAI3B,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,eAAM,CAAC,aAAa,CAAC,CAAC;QACxC,IAAI,CAAC,iBAAiB,GAAG,IAAI,8BAAiB,CAAC,aAAa,CAAC,CAAC;QAC9D,IAAI,CAAC,OAAO,GAAG,IAAI,8BAAa,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACzD,IAAI,CAAC,uBAAuB,GAAG,IAAI,oCAAuB,EAAE,CAAC;IACjE,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;IACvF,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC7D,CAAC;IAED,IAAY,cAAc;QACtB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IAChE,CAAC;IAEM,SAAS,CAAC,iBAA6C;QAC1D,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,iBAAiB,CAAC;QAC9D,OAAO,IAAI,CAAC;IAChB,CAAC;IAEM,QAAQ,CAAC,gBAAgC;QAC5C,IAAI,CAAC,uBAAuB,CAAC,YAAY,GAAG,gBAAgB,CAAC;QAC7D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,8DAA8D;IACvD,IAAI,CAAC,KAAsB,EAAE,GAAG,IAAW;QAC9C,IAAI,KAAK,KAAK,KAAK,EAAE;YACjB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;gBACvB,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpC;YACD,OAAO,KAAK,CAAC;SAChB;QACD,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,CAAC;IACtC,CAAC;IAEM,UAAU,CAAC,IAAY,EAAE,QAAgB,EAAE,IAAuB;QACrE,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,IAAI,EAAE,CAAC;SACjB;QACD,MAAM,eAAe,GAAG,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/D,IAAI;YACA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;YACvB,MAAM,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;SAClD;QAAC,OAAO,CAAC,EAAE;YACR,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;SAC7B;IACL,CAAC;IAEM,MAAM,CAAC,IAAuB;QACjC,MAAM,eAAe,GAAG,eAAe,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC/D,6DAA6D;QAC7D,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO,eAAe,EAAE,CAAC;SAC5B;QACD,IAAI;YACA,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;YAChD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACxC,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;SAClD;QAAC,OAAO,CAAC,EAAE;YACR,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;SAC7B;IACL,CAAC;IAEO,KAAK,CAAC,IAAY,EAAE,WAAoB;QAC5C,IAAI,CAAC,IAAI,EAAE;YACP,OAAO,EAAE,CAAC;SACb;QACD,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;QAC5D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,WAAW,CAAC,IAAgB,EAAE,EAAqB;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC;QAC/B,MAAM,OAAO,GAAG,CAAC,CAAS,EAAQ,EAAE;YAChC,MAAM,QAAQ,GAAG,CAAC,GAAW,EAAQ,EAAE;gBACnC,IAAI,GAAG,EAAE;oBACL,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;iBAClB;gBACD,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE;oBACf,6EAA6E;oBAC7E,YAAY,CAAC,GAAS,EAAE,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACzC,OAAO,SAAS,CAAC;iBACpB;gBACD,OAAO,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1B,CAAC,CAAC;YACF,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,sEAAsE;YACtE,WAAW;YACX,IAAI,CAAC,IAAI,UAAU,IAAI,IAAI,CAAC,cAAc,EAAE;gBACxC,OAAO,EAAE,EAAE,CAAC;aACf;YACD,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC;YAC1B,IAAI,IAAI,CAAC,cAAc,EAAE;gBACrB,OAAO,QAAQ,EAAE,CAAC;aACrB;YACD,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACpB,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;YACnB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;YACzB,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC;YACnC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,eAAe,EAAQ,EAAE;gBACzD,IAAI,GAAG,EAAE;oBACL,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;oBACnB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC;iBACxB;gBACD,IAAI,CAAC,eAAe,EAAE;oBAClB,OAAO,QAAQ,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC;iBAC3D;gBACD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE;oBAC1B,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,GAAG,EAAE,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;iBACxF;qBAAM,IAAI,eAAe,CAAC,GAAG,EAAE;oBAC5B,OAAO,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;iBACtD;gBACD,OAAO,QAAQ,EAAE,CAAC;YACtB,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;QACF,OAAO,CAAC,CAAC,CAAC,CAAC;IACf,CAAC;IAEO,YAAY,CAAC,SAAmB,EAAE,EAA2B;QACjE,IAAI;YACA,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,WAAW,EAAQ,EAAE;gBACnE,IAAI,GAAG,EAAE;oBACL,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;iBAClB;gBACD,IAAI,CAAC,WAAW,EAAE;oBACd,OAAO,EAAE,CAAC,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC,CAAC;iBACjE;gBACD,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE;oBACtB,IAAI,IAAI,CAAC,cAAc,EAAE;wBACrB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,EAAG,SAAwB,EAAE,CAAC,CAAC;qBACvE;oBACD,iEAAiE;oBACjE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;iBAC3B;gBACD,IAAI,WAAW,CAAC,GAAG,EAAE;oBACjB,IAAI,IAAI,CAAC,cAAc,EAAE;wBACrB,OAAO,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;qBACjF;oBACD,iEAAiE;oBACjE,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;iBAC3B;gBACD,sEAAsE;gBACtE,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;gBACnB,IAAI,CAAC,cAAc,IAAI,CAAC,CAAC;gBACzB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAClD,CAAC,CAAC,CAAC;SACN;QAAC,OAAO,CAAC,EAAE;YACR,EAAE,CAAC,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEO,mBAAmB;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE;YACxD,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;YAC3B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;SACxD;IACL,CAAC;IAEO,OAAO,CAAC,EAA2B;QACvC,iEAAiE;QACjE,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;QACnB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;IAClD,CAAC;IAEO,OAAO,CAAC,GAAQ,EAAE,EAAyB;QAC/C,IAAI;YACA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE;gBAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;aAClC;iBAAM;gBACH,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;YACD,EAAE,EAAE,CAAC;SACR;QAAC,OAAO,CAAC,EAAE;YACR,EAAE,CAAC,CAAC,CAAC,CAAC;SACT;IACL,CAAC;IAEO,MAAM,CAAC,gBAAgB,CAAC,IAAuB;QACnD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,8DAA8D;QAC9D,OAAO,CAAC,GAA6B,EAAE,GAAG,IAAW,EAAQ,EAAE;YAC3D,IAAI,GAAG,EAAE;gBACL,IAAI,WAAW,EAAE;oBACb,MAAM,GAAG,CAAC;iBACb;gBACD,WAAW,GAAG,IAAI,CAAC;gBACnB,IAAI,CAAC,GAAG,CAAC,CAAC;gBACV,OAAO;aACV;YACD,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAClB,CAAC,CAAC;IACN,CAAC;CACJ;AAnOD,0CAmOC"}