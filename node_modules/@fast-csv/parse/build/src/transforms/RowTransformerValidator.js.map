{"version": 3, "file": "RowTransformerValidator.js", "sourceRoot": "", "sources": ["../../../src/transforms/RowTransformerValidator.ts"], "names": [], "mappings": ";;;;;;AAAA,0EAA2C;AAC3C,oCASkB;AAIlB,MAAa,uBAAuB;IAApC;QAsCY,kBAAa,GAAmC,IAAI,CAAC;QAErD,kBAAa,GAA2B,IAAI,CAAC;IAiDzD,CAAC;IAxFG,wDAAwD;IAChD,MAAM,CAAC,eAAe,CAC1B,iBAA6C;QAE7C,IAAI,uBAAe,CAAC,iBAAiB,CAAC,EAAE;YACpC,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;gBACjD,IAAI,WAAW,GAAa,IAAI,CAAC;gBACjC,IAAI;oBACA,WAAW,GAAG,iBAAiB,CAAC,GAAG,CAAC,CAAC;iBACxC;gBAAC,OAAO,CAAC,EAAE;oBACR,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;iBAChB;gBACD,OAAO,EAAE,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACjC,CAAC,CAAC;SACL;QACD,OAAO,iBAAiB,CAAC;IAC7B,CAAC;IAEO,MAAM,CAAC,eAAe,CAAgB,gBAAgC;QAC1E,IAAI,sBAAc,CAAC,gBAAgB,CAAC,EAAE;YAClC,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;gBACjD,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YACtD,CAAC,CAAC;SACL;QACD,OAAO,CAAC,GAAM,EAAE,EAA2B,EAAQ,EAAE;YACjD,gBAAgB,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,OAAO,EAAE,MAAM,EAAQ,EAAE;gBACjD,IAAI,GAAG,EAAE;oBACL,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC;iBAClB;gBACD,IAAI,OAAO,EAAE;oBACT,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;iBAC7C;gBACD,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;YACrD,CAAC,CAAC,CAAC;QACP,CAAC,CAAC;IACN,CAAC;IAMD,IAAW,YAAY,CAAC,iBAA6C;QACjE,IAAI,CAAC,2BAAU,CAAC,iBAAiB,CAAC,EAAE;YAChC,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;SAC7D;QACD,IAAI,CAAC,aAAa,GAAG,uBAAuB,CAAC,eAAe,CAAC,iBAAiB,CAAC,CAAC;IACpF,CAAC;IAED,IAAW,YAAY,CAAC,gBAAgC;QACpD,IAAI,CAAC,2BAAU,CAAC,gBAAgB,CAAC,EAAE;YAC/B,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;SAC5D;QACD,IAAI,CAAC,aAAa,GAAG,uBAAuB,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;IACnF,CAAC;IAEM,oBAAoB,CAAC,GAAM,EAAE,EAA2B;QAC3D,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,cAAc,EAAQ,EAAE;YACpE,IAAI,YAAY,EAAE;gBACd,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;aAC3B;YACD,IAAI,CAAC,cAAc,EAAE;gBACjB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;aACjD;YACD,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,WAAW,EAAE,gBAAgB,EAAQ,EAAE;gBAC9E,IAAI,WAAW,EAAE;oBACb,OAAO,EAAE,CAAC,WAAW,CAAC,CAAC;iBAC1B;gBACD,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE;oBAC/C,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;iBAC7F;gBACD,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5D,CAAC,CAAC,CAAC;QACP,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,eAAe,CAAC,GAAM,EAAE,EAA2B;QACvD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,EAAE,CAAC,IAAI,EAAG,GAAkB,CAAC,CAAC;SACxC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;IAEO,aAAa,CAAC,GAAM,EAAE,EAA2B;QACrD,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACvC,CAAC;CACJ;AAzFD,0DAyFC"}