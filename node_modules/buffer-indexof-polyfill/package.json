{"name": "buffer-indexof-polyfill", "version": "1.0.2", "description": "This is a polyfill for Buffer#indexOf introduced in NodeJS 4.0.", "main": "index.js", "scripts": {"test": "mocha", "lint": "eslint .", "fix": "eslint . --fix"}, "author": "https://github.com/sarosia", "license": "MIT", "engines": {"node": ">=0.10"}, "repository": {"type": "git", "url": "git+https://github.com/sarosia/buffer-indexof-polyfill.git"}, "devDependencies": {"chai": "^3.3.0", "eslint": "^1.10.3", "mocha": "^2.3.3"}, "keywords": ["buffer", "indexof", "polyfill"], "bugs": {"url": "https://github.com/sarosia/buffer-indexof-polyfill/issues"}, "homepage": "https://github.com/sarosia/buffer-indexof-polyfill#readme"}